{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: g++ 生成活动文件", "command": "/usr/bin/g++", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}, {"type": "cppbuild", "label": "C/C++: g++-11 生成活动文件", "command": "/usr/bin/g++-11", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "调试器生成的任务。"}]}