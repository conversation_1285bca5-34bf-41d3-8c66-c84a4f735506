{"version": "0.2.0", "configurations": [{"name": "(gdb) 启动", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/test", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "preLaunchTask": "C/C++: g++ 生成活动文件", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}, {"name": "C/C++: g++-11 构建和调试活动文件", "type": "cppdbg", "request": "launch", "program": "${fileDirname}/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "C/C++: g++-11 生成活动文件", "miDebuggerPath": "/usr/bin/gdb"}]}